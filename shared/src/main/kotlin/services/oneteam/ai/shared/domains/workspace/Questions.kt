package services.oneteam.ai.shared.domains.workspace

// https://stackoverflow.com/questions/72487782/jsonclassdiscriminator-doesnt-change-json-class-discriminator

import kotlinx.datetime.LocalDate
import kotlinx.serialization.*
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.*
import services.oneteam.ai.shared.domains.event.getEnumFromSerialName
import services.oneteam.ai.shared.domains.workspace.BaseSection.Section
import services.oneteam.ai.shared.domains.workspace.BaseSection.TextQuestion
import services.oneteam.ai.shared.domains.workspace.CommonQuestionProperties.TableQuestionProperties
import services.oneteam.ai.shared.domains.workspace.validation.*
import java.math.BigDecimal
import kotlin.collections.flatten

// https://stackoverflow.com/questions/75257762/how-can-i-json-encode-bigdecimal-and-biginteger-in-kotlinx-serialization-without
typealias BigDecimalJson = @Serializable(with = BigDecimalSerializer::class) BigDecimal

@Serializable
enum class QuestionType(val serializer: DeserializationStrategy<BaseSection.BaseQuestion>, val value: String) {
    @SerialName("text")
    TEXT(TextQuestion.serializer(), "text"),

    @SerialName("table")
    TABLE(BaseSection.TableQuestion.serializer(), "table"),

    @SerialName("select")
    SELECT(BaseSection.SelectQuestion.serializer(), "select"),

    @SerialName("multiSelect")
    MULTISELECT(BaseSection.MultiSelectQuestion.serializer(), "multiSelect"),

    @SerialName("boolean")
    BOOLEAN(BaseSection.BooleanQuestion.serializer(), "boolean"),

    @SerialName("number")
    NUMBER(BaseSection.NumberQuestion.serializer(), "number"),

    @SerialName("date")
    DATE(BaseSection.DateQuestion.serializer(), "date"),

    @SerialName("files")
    FILES(BaseSection.FilesQuestion.serializer(), "files"),

    @SerialName("json")
    JSON(BaseSection.JsonQuestion.serializer(), "json"),

    @SerialName("list")
    LIST(BaseSection.ListQuestion.serializer(), "list"),

    @SerialName("schema")
    SCHEMA(BaseSection.SchemaQuestion.serializer(), "schema"),

    @SerialName("chart")
    CHART(BaseSection.ChartQuestion.serializer(), "chart");

    override fun toString(): String {
        return this.value
    }
}

interface BaseConfigValidator {
    fun validateConfiguration(path: String): Errors {
        return Errors()
    }
}

/**
 * To avoid generics serialization issues, each question type is a separate class. This is a bit verbose, but it works.
 * Perhaps this could be refactored to have a typed question in the next iteration.
 */
@Serializable(with = SectionSerializer::class)
sealed interface BaseSection : BaseConfigValidator {

    @Serializable
    @JvmInline
    value class Id(val value: String)

    @Serializable
    @JvmInline
    value class Name(val value: String)

    @Serializable
    @JvmInline
    value class Level(val value: Int)

    @Serializable
    @SerialName("section")
    data class Section(
        val id: Id,
        val name: Name,
        val level: Level,
        val content: List<BaseSection>? = emptyList(),
    ) : BaseSection {
        override fun validateConfiguration(path: String): Errors {
            val errors = Errors()
            content?.forEachIndexed { index, section ->
                section.validateConfiguration("$path.content.$index").let { errors.addAll(it) }
            }
            return errors
        }

        fun findQuestion(questionId: Id): BaseQuestion? {
            return content?.mapNotNull {
                when (it) {
                    is BaseQuestion -> it
                    is Section -> it.findQuestion(questionId)
                }
            }?.firstOrNull { it.id == questionId }
        }

        fun findQuestionByIdentifier(string: String): BaseQuestion? {
            return content?.mapNotNull {
                when (it) {
                    is BaseQuestion -> it
                    is Section -> it.findQuestionByIdentifier(string)
                }
            }?.firstOrNull { it.identifier == string }
        }

        fun listAllQuestions(): List<BaseQuestion> {
            return content?.mapNotNull {
                when (it) {
                    is BaseQuestion -> listOf(it)
                    is Section -> it.listAllQuestions()
                }
            }?.flatten() ?: emptyList()
        }
    }

    @Serializable(with = QuestionSerializer::class)
    sealed class BaseQuestion : BaseSection {
        abstract val id: Id
        abstract val description: String?
        abstract val identifier: String
        abstract val text: String
        abstract val type: QuestionType
        abstract val properties: CommonQuestionProperties?

        open fun toIdentifierMap(): Map<String, BaseQuestion> {
            return mapOf(identifier to this)
        }

        override fun validateConfiguration(path: String): Errors {
            val errors = Errors()
            if (text.isBlank()) {
                errors.add(
                    ConstraintError(
                        Field("name"),
                        Type("required"),
                        Path("$path.text"),
                        ConstraintDetail("text is required"),
                        Message("errors.common.name.required")
                    )
                )
            }
            if (identifier.isBlank()) {
                errors.add(
                    ConstraintError(
                        Field("identifier"),
                        Type("required"),
                        Path("$path.identifier"),
                        ConstraintDetail("identifier is required"),
                        Message("errors.common.identifier.required")
                    )
                )
            }
            val regex = "^[a-zA-Z0-9_]+$".toRegex()
            if (!regex.matches(identifier)) {
                errors.add(
                    ConstraintError(
                        Field("identifier"),
                        Type("pattern"),
                        Path("$path.identifier"),
                        ConstraintDetail("errors.common.identifier.alphaNumeric"),
                        Message("errors.common.identifier.alphaNumeric")
                    )
                )
            }
            return errors
        }
    }

    @Serializable
    @SerialName("text")
    data class TextQuestion @OptIn(ExperimentalSerializationApi::class) constructor(
        override val id: Id,
        override val description: String? = null,
        override val identifier: String,
        override val text: String,
        @OptIn(ExperimentalSerializationApi::class)
        @EncodeDefault override val type: QuestionType = QuestionType.TEXT,
        override val properties: CommonQuestionProperties.TextQuestionProperties? = null,
    ) : BaseQuestion() {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            properties?.validateConfiguration("$path.properties")?.let { errors.addAll(it) }
            return errors
        }
    }

    @Serializable
    @SerialName("table")
    data class TableQuestion @OptIn(ExperimentalSerializationApi::class) constructor(
        override val id: Id,
        override val description: String? = null,
        override val identifier: String,
        override val text: String,
        @OptIn(ExperimentalSerializationApi::class)
        @EncodeDefault override val type: QuestionType = QuestionType.TABLE,
        override val properties: TableQuestionProperties? = null,
    ) : BaseQuestion() {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            properties?.columns?.forEach { question ->
                question.validateConfiguration("$path.properties.columns.${question.id.value}")
                    .let { errors.addAll(it) }
            }
            properties?.charts?.forEach { question ->
                question.validateConfiguration("$path.properties.charts.${question.id.value}")
                    .let { errors.addAll(it) }
            }
            return errors
        }

        override fun toIdentifierMap(): Map<String, BaseQuestion> {
            return properties?.columns?.associateBy { it.identifier } ?: emptyMap()
        }
    }

    @Serializable
    @SerialName("json")
    data class JsonQuestion @OptIn(ExperimentalSerializationApi::class) constructor(
        override val id: Id,
        override val description: String? = null,
        override val identifier: String,
        override val text: String,
        @OptIn(ExperimentalSerializationApi::class)
        @EncodeDefault override val type: QuestionType = QuestionType.JSON,
        override val properties: CommonQuestionProperties.JsonQuestionProperties? = null,
    ) : BaseQuestion() {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            properties?.items?.forEach { question ->
                question.validateConfiguration("$path.properties.items.${question.id.value}").let { errors.addAll(it) }
            }
            return errors
        }

        override fun toIdentifierMap(): Map<String, BaseQuestion> {
            return properties?.items?.associateBy { it.identifier } ?: emptyMap()
        }
    }

    @Serializable
    @SerialName("list")
    data class ListQuestion @OptIn(ExperimentalSerializationApi::class) constructor(
        override val id: Id,
        override val description: String? = null,
        override val identifier: String,
        override val text: String,
        @OptIn(ExperimentalSerializationApi::class)
        @EncodeDefault override val type: QuestionType = QuestionType.LIST,
        override val properties: CommonQuestionProperties.ListQuestionProperties? = null,
    ) : BaseQuestion() {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            properties?.items?.forEach { question ->
                question.validateConfiguration("$path.properties.items.${question.id.value}").let { errors.addAll(it) }
            }
            return errors
        }

        override fun toIdentifierMap(): Map<String, BaseQuestion> {
            return properties?.items?.associateBy { it.identifier } ?: emptyMap()
        }
    }

    @Serializable
    @SerialName("select")
    data class SelectQuestion @OptIn(ExperimentalSerializationApi::class) constructor(
        override val id: Id,
        override val description: String? = null,
        override val identifier: String,
        @OptIn(ExperimentalSerializationApi::class)
        @EncodeDefault override val type: QuestionType = QuestionType.SELECT,
        override val text: String,
        override val properties: CommonQuestionProperties.SelectQuestionProperties? = null,
    ) : BaseQuestion() {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            errors.add(
                ConstraintError(
                    Field("description"),
                    Type("value.duplicate"),
                    Path("$path.description"),
                    ConstraintDetail("duplicate value not allowed"),
                    Message("duplicate value not allowed")
                )
            )
//            properties?.validateConfiguration("$path.properties").let { errors.addAll(it) }
            return errors
        }
    }

    @Serializable
    @SerialName("multiselect")
    data class MultiSelectQuestion @OptIn(ExperimentalSerializationApi::class) constructor(
        override val id: Id,
        override val description: String? = null,
        override val identifier: String,
        @OptIn(ExperimentalSerializationApi::class)
        @EncodeDefault override val type: QuestionType = QuestionType.MULTISELECT,
        override val text: String,
        override val properties: CommonQuestionProperties.MultiSelectQuestionProperties? = null,
    ) : BaseQuestion()

    @Serializable
    @SerialName("boolean")
    data class BooleanQuestion @OptIn(ExperimentalSerializationApi::class) constructor(
        override val id: Id,
        override val description: String? = null,
        override val identifier: String,
        @OptIn(ExperimentalSerializationApi::class)
        @EncodeDefault override val type: QuestionType = QuestionType.BOOLEAN,
        override val text: String,
        override val properties: CommonQuestionProperties.BooleanQuestionProperties? = null,
    ) : BaseQuestion()

    @Serializable
    @SerialName("number")
    data class NumberQuestion @OptIn(ExperimentalSerializationApi::class) constructor(
        override val id: Id,
        override val description: String? = null,
        override val identifier: String,
        @OptIn(ExperimentalSerializationApi::class)
        @EncodeDefault override val type: QuestionType = QuestionType.NUMBER,
        override val text: String,
        override val properties: CommonQuestionProperties.NumberQuestionProperties? = null,
    ) : BaseQuestion() {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            properties?.validateConfiguration("$path.properties").let { errors.addAll(it) }
            return errors
        }
    }

    @Serializable
    @SerialName("date")
    data class DateQuestion @OptIn(ExperimentalSerializationApi::class) constructor(
        override val id: Id,
        override val description: String? = null,
        override val identifier: String,
        @OptIn(ExperimentalSerializationApi::class)
        @EncodeDefault override val type: QuestionType = QuestionType.DATE,
        override val text: String,
        override val properties: CommonQuestionProperties.DateQuestionProperties? = null,
    ) : BaseQuestion() {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            properties?.validateConfiguration("$path.properties").let { errors.addAll(it) }
            return errors
        }
    }

    @Serializable
    @SerialName("files")
    data class FilesQuestion @OptIn(ExperimentalSerializationApi::class) constructor(
        override val id: Id,
        override val description: String? = null,
        override val identifier: String,
        @OptIn(ExperimentalSerializationApi::class)
        @EncodeDefault override val type: QuestionType = QuestionType.FILES,
        override val text: String,
        override val properties: CommonQuestionProperties.FilesQuestionProperties? = null,
    ) : BaseQuestion() {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            properties?.validateConfiguration("$path.properties").let { errors.addAll(it) }
            return errors
        }
    }

    @Serializable
    @SerialName("schema")
    data class SchemaQuestion @OptIn(ExperimentalSerializationApi::class) constructor(
        override val id: Id,
        override val description: String? = null,
        override val identifier: String,
        @OptIn(ExperimentalSerializationApi::class)
        @EncodeDefault override val type: QuestionType = QuestionType.SCHEMA,
        override val text: String,
        override val properties: CommonQuestionProperties.SchemaQuestionProperties? = null,
    ) : BaseQuestion()

    @Serializable
    @SerialName("chart")
    data class ChartQuestion @OptIn(ExperimentalSerializationApi::class) constructor(
        override val id: Id,
        override val description: String? = null,
        override val identifier: String,
        @OptIn(ExperimentalSerializationApi::class)
        @EncodeDefault override val type: QuestionType = QuestionType.CHART,
        override val text: String,
        override val properties: CommonQuestionProperties.ChartQuestionProperties? = null,
    ) : BaseQuestion() {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            properties?.validateConfiguration("$path.properties").let { errors.addAll(it) }
            return errors
        }
    }
}


@Serializable
sealed interface CommonQuestionProperties : BaseConfigValidator {
    val required: Boolean?
    val allowReuseAcrossForms: Boolean?
    val disabled: Boolean?
    val hidden: Boolean?

    @Serializable
    data class TextQuestionProperties(
        val maxLength: Int? = null,
        val minLength: Int? = null,
        val defaultValue: String? = null,
        val placeholder: String? = null,
        val isTextArea: Boolean? = null,
        override val required: Boolean? = false,
        override val allowReuseAcrossForms: Boolean? = null,
        override val hidden: Boolean? = null,
        override val disabled: Boolean? = null
    ) : CommonQuestionProperties {
        override fun validateConfiguration(path: String): Errors {
            val errors = Errors()
            if (minLength != null && !defaultValue.isNullOrEmpty() && defaultValue.length < minLength) {
                errors.add(
                    ConstraintError(
                        Field("defaultValue"),
                        Type("lessThanMinLength"),
                        Path("$path.defaultValue"),
                        ConstraintDetail("default length should be greater than minLength"),
                        Message("default length should be greater than minLength")
                    )
                )
            }
            if (maxLength != null && defaultValue != null && defaultValue.length > maxLength) {
                errors.add(
                    ConstraintError(
                        Field("defaultValue"),
                        Type("greaterThanMaxLength"),
                        Path("$path.defaultValue"),
                        ConstraintDetail("default length should be less than maxLength"),
                        Message("default length should be less than maxLength")
                    )
                )
            }
            if (maxLength != null && minLength != null && maxLength < minLength) {
                errors.add(
                    ConstraintError(
                        Field("minLength"),
                        Type("greaterThanMaxLength"),
                        Path("$path.minLength"),
                        ConstraintDetail("minLength is greater than maxLength"),
                        Message("errors.configurationForm.question.minLength.greaterThanMaxLength")
                    )
                )
                errors.add(
                    ConstraintError(
                        Field("maxLength"),
                        Type("lessThanMinLength"),
                        Path("$path.maxLength"),
                        ConstraintDetail("maxLength is less than minLength"),
                        Message("errors.configurationForm.question.maxLength.lessThanMinLength")
                    )
                )
            }
            return errors
        }
    }

    @Serializable
    data class TableQuestionProperties(
        val columns: List<BaseSection.BaseQuestion>? = emptyList(),
        val charts: List<BaseSection.ChartQuestion>? = emptyList(),
        override val required: Boolean = false,
        override val allowReuseAcrossForms: Boolean? = null,
        override val disabled: Boolean? = null,
        override val hidden: Boolean? = null
    ) : CommonQuestionProperties

    @Serializable
    data class JsonQuestionProperties(
        val items: List<BaseSection.BaseQuestion>? = emptyList(),
        override val required: Boolean = false,
        override val allowReuseAcrossForms: Boolean? = null,
        override val disabled: Boolean? = null,
        override val hidden: Boolean? = null
    ) : CommonQuestionProperties

    @Serializable
    data class ListQuestionProperties(
        val maxLength: Int? = null,
        val minLength: Int? = null,
        val items: List<BaseSection.BaseQuestion>? = emptyList(),
        override val required: Boolean = false,
        override val allowReuseAcrossForms: Boolean? = null,
        override val hidden: Boolean? = null,
        override val disabled: Boolean? = null
    ) : CommonQuestionProperties {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            if (maxLength != null && minLength != null && maxLength < minLength) {
                errors.add(
                    ConstraintError(
                        Field("minLength"),
                        Type("greaterThanMaxLength"),
                        Path("$path.minLength"),
                        ConstraintDetail("minLength is greater than maxLength"),
                        Message("errors.configurationForm.question.minLength.greaterThanMaxLength")
                    )
                )
                errors.add(
                    ConstraintError(
                        Field("maxLength"),
                        Type("lessThanMinLength"),
                        Path("$path.maxLength"),
                        ConstraintDetail("maxLength is less than minLength"),
                        Message("errors.configurationForm.question.maxLength.lessThanMinLength")
                    )
                )
            }
            return errors
        }
    }

    @Serializable
    data class SelectOption(
        val key: Int? = null,
        val value: String,
        val label: String
    )

    @Serializable
    data class SelectQuestionProperties(
        val defaultValue: String? = null,
        val options: List<SelectOption>,
        val placeholder: String? = null,
        val isMultiSelect: Boolean? = null,
        override val required: Boolean? = false,
        override val allowReuseAcrossForms: Boolean? = null,
        override val disabled: Boolean? = null,
        override val hidden: Boolean? = null
    ) : CommonQuestionProperties {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            val values = options.map { it.value }
            val duplicatedValues = values.groupingBy { it }.eachCount().filter { it.value > 1 }.keys
            if (duplicatedValues.isNotEmpty()) {
                errors.add(
                    ConstraintError(
                        Field("options"),
                        Type("value.duplicate"),
                        Path("$path.options"),
                        ConstraintDetail("duplicate value not allowed"),
                        Message("duplicate value not allowed")
                    )
                )
            }
            return errors
        }
    }

    @Serializable
    @OptIn(ExperimentalSerializationApi::class)
    data class MultiSelectQuestionProperties(
        val defaultValue: List<String>? = null,
        val options: List<SelectOption>,
        val placeholder: String? = null,
        @EncodeDefault val isMultiSelect: Boolean? = true,
        override val required: Boolean? = false,
        override val allowReuseAcrossForms: Boolean? = null,
        override val disabled: Boolean? = null,
        override val hidden: Boolean? = null
    ) : CommonQuestionProperties

    @Serializable
    data class BooleanQuestionProperties(
        val defaultValue: Boolean? = null,
        val trueText: String? = null,
        val falseText: String? = null,
        override val required: Boolean? = false,
        override val allowReuseAcrossForms: Boolean? = null,
        override val disabled: Boolean? = null,
        override val hidden: Boolean? = null
    ) : CommonQuestionProperties

    enum class NumberType {
        @SerialName("number")
        NUMBER,

        @SerialName("accounting")
        ACCOUNTING,

        @SerialName("percentage")
        PERCENTAGE,
    }

    // use BigDecimal for number
    @Serializable
    data class NumberQuestionProperties(
        val decimalPlaces: Int? = null,
        @EncodeDefault(EncodeDefault.Mode.NEVER) val min: BigDecimalJson? = null,
        @EncodeDefault(EncodeDefault.Mode.NEVER) val max: BigDecimalJson? = null,
        val defaultValue: BigDecimalJson? = null,
        val placeholder: String? = null,
        val type: NumberType? = null,
        override val required: Boolean? = false,
        override val allowReuseAcrossForms: Boolean? = null,
        override val disabled: Boolean? = null,
        override val hidden: Boolean? = null
    ) : CommonQuestionProperties {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            if (min != null && max != null && min > max) {
                errors.add(ConstraintError.buildMinGreaterThanMaxError(path))
            }
            if (min != null && defaultValue != null && defaultValue < min) {
                errors.add(ConstraintError.buildDefaultLessThanMinError(path))
            }
            if (max != null && defaultValue != null && defaultValue > max) {
                errors.add(ConstraintError.buildDefaultGreaterThanMaxError(path))
            }
            return errors
        }
    }

    @Serializable
    data class DateQuestionProperties(
        val min: String? = null,
        val max: String? = null,
        val defaultValue: String? = null,
        val placeholder: String? = null,
        override val required: Boolean? = false,
        override val allowReuseAcrossForms: Boolean? = null,
        override val disabled: Boolean? = null,
        override val hidden: Boolean? = null
    ) : CommonQuestionProperties {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            if (min != null && max != null) {
                val minDate = LocalDate.parse(min, LocalDate.Formats.ISO)
                val maxDate = LocalDate.parse(max, LocalDate.Formats.ISO)
                if (minDate > maxDate) {
                    errors.add(ConstraintError.buildMinGreaterThanMaxError(path))
                }
            }
            if (min != null && !defaultValue.isNullOrEmpty()) {
                val minDate = LocalDate.parse(min, LocalDate.Formats.ISO)
                val defaultDate = LocalDate.parse(defaultValue, LocalDate.Formats.ISO)
                if (defaultDate < minDate) {
                    errors.add(ConstraintError.buildDefaultLessThanMinError(path))
                }
            }
            if (max != null && !defaultValue.isNullOrEmpty()) {
                val maxDate = LocalDate.parse(max, LocalDate.Formats.ISO)
                val defaultDate = LocalDate.parse(defaultValue, LocalDate.Formats.ISO)
                if (defaultDate > maxDate) {
                    errors.add(ConstraintError.buildDefaultGreaterThanMaxError(path))
                }
            }
            return errors
        }
    }

    @Serializable
    enum class FilesFileFormats {
        @SerialName("audio")
        AUDIO,

        @SerialName("document")
        DOCUMENT,

        @SerialName("image")
        IMAGE,

        @SerialName("pdf")
        PDF,

        @SerialName("presentation")
        PRESENTATION,

        @SerialName("spreadsheet")
        SPREADSHEET,

        @SerialName("video")
        VIDEO
    }

    @Serializable
    data class FilesQuestionProperties(
        val min: Int? = null,
        val max: Int? = null,
        val maxFileSizeMB: Int? = null,
        val restrictedFileTypes: List<FilesFileFormats>? = null,
        val placeholder: String? = null,
        override val required: Boolean? = false,
        override val allowReuseAcrossForms: Boolean? = null,
        override val disabled: Boolean? = null,
        override val hidden: Boolean? = null
    ) : CommonQuestionProperties {
        val MAX_FILE_SIZE_MB = 100
        val MAX_NUMBER_OF_FILES = 25
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)

            if (max != null && max > MAX_NUMBER_OF_FILES) {
                errors.add(
                    ConstraintError(
                        Field("files"),
                        Type("max.upperLimit"),
                        Path("$path.files"),
                        ConstraintDetail("max number of documents CANNOT be greater than $MAX_NUMBER_OF_FILES"),
                        Message("max number of documents CANNOT be greater than $MAX_NUMBER_OF_FILES")
                    )
                )
            }

            if (maxFileSizeMB != null && maxFileSizeMB > MAX_FILE_SIZE_MB) {
                errors.add(
                    ConstraintError(
                        Field("files"),
                        Type("maxFileSize.greaterThanMaximum"),
                        Path("$path.files"),
                        ConstraintDetail("maxFileSizeMB cannot be greater than $MAX_FILE_SIZE_MB"),
                        Message("maxFileSizeMB cannot be greater than $MAX_FILE_SIZE_MB")
                    )
                )
            }

            if (min != null && max != null && min > max) {
                errors.add(
                    ConstraintError(
                        Field("files"),
                        Type("min.greaterThanMax"),
                        Path("$path.files"),
                        ConstraintDetail("min should be less than or equal to maxs"),
                        Message("min number of files should not be greater than max number of files")
                    )
                )
                errors.add(
                    ConstraintError(
                        Field("files"),
                        Type("max.lessThanMin"),
                        Path("$path.files"),
                        ConstraintDetail("maxLength is less than minLength"),
                        Message("errors.configurationForm.question.maxLength.lessThanMinLength")
                    )
                )
            }
            return errors
        }
    }

    @Serializable
    data class SchemaQuestionProperties(
        override val required: Boolean = false,
        override val allowReuseAcrossForms: Boolean? = null,
        override val disabled: Boolean? = null,
        override val hidden: Boolean? = null
    ) : CommonQuestionProperties

    @Serializable
    enum class ChartType {
        @SerialName("line")
        LINE,
        @SerialName("bar")
        BAR,
        @SerialName("pie")
        PIE
    }
    @Serializable(with = ChartConfigSerializer::class)
    sealed class ChartConfig : BaseConfigValidator{
        abstract val type: ChartType
        abstract val subType: String?
        abstract val swapColumns: Boolean?
        override fun validateConfiguration(path: String): Errors {
            return Errors()
        }
    }

    @Serializable
    data class LineChartConfig(
        override val type: ChartType = ChartType.LINE,
        override val subType: String? = null,
        override val swapColumns: Boolean? = null,
        val xAxis: List<String>,
        val series: List<String>
    ) : ChartConfig() {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            errors.add(
                ConstraintError(
                    Field("series"),
                    Type("greaterThanMaxLength"),
                    Path("$path.series"),
                    ConstraintDetail("minLength is greater than maxLength"),
                    Message("errors.configurationForm.question.minLength.greaterThanMaxLength")
                )
            )
            if (xAxis.isEmpty()) {
                errors.add(
                    ConstraintError(
                        Field("minLength"),
                        Type("greaterThanMaxLength"),
                        Path("$path.xAxis"),
                        ConstraintDetail("minLength is greater than maxLength"),
                        Message("errors.configurationForm.question.minLength.greaterThanMaxLength")
                    )
                )
            }
            return errors
        }
    }

    @Serializable
    data class BarChartConfig(
        override val type: ChartType = ChartType.BAR,
        override val subType: String? = null,
        override val swapColumns: Boolean? = null,
        val xAxis: List<String>,
        val series: List<String>
    ) : ChartConfig()

    @Serializable
    data class PieChartConfig(
        override val type: ChartType = ChartType.PIE,
        override val subType: String? = null,
        override val swapColumns: Boolean? = null,
        val groupBy: List<String>,
        val series: List<String>,
        val rowIndex: Int? = null
    ) : ChartConfig()


    @Serializable
    data class ChartQuestionProperties(
        val chartConfig: ChartConfig,
        override val required: Boolean = false,
        override val allowReuseAcrossForms: Boolean? = null,
        override val disabled: Boolean? = null,
        override val hidden: Boolean? = null
    ): CommonQuestionProperties {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            chartConfig.validateConfiguration("$path.chartConfig").let { errors.addAll(it) }
            return errors
        }
    }

    object ChartConfigSerializer : JsonContentPolymorphicSerializer<ChartConfig>(ChartConfig::class) {
        override fun selectDeserializer(element: JsonElement): DeserializationStrategy<ChartConfig> {
            val type = element.jsonObject["type"]?.jsonPrimitive?.content
            return when (type) {
                "line" -> LineChartConfig.serializer()
                "bar" -> BarChartConfig.serializer()
                "pie" -> PieChartConfig.serializer()
                else -> throw IllegalArgumentException("Unknown chart type: $type")
            }
        }
    }


}



/**
 * Handles deserialization of Sections, which can be either a Section or a Question.
 * Questions also have different types.
 * This can perhaps be improved over time, but for now, this is a good starting point.
 */
// https://medium.com/livefront/intro-to-polymorphism-with-kotlinx-serialization-b8f5f1cedc99
object SectionSerializer : JsonContentPolymorphicSerializer<BaseSection>(
    BaseSection::class,
) {
    override fun selectDeserializer(
        element: JsonElement,
    ): DeserializationStrategy<BaseSection> {
        val jsonObject = element.jsonObject
        return when {
            jsonObject.containsKey("content") -> Section.serializer()
            jsonObject.containsKey("type") -> BaseSection.BaseQuestion.serializer()

            else -> throw IllegalArgumentException(
                "Unsupported Base type when deserializing",
            )
        }
    }
}

object QuestionSerializer : JsonContentPolymorphicSerializer<BaseSection.BaseQuestion>(
    BaseSection.BaseQuestion::class,
) {
    override fun selectDeserializer(
        element: JsonElement,
    ): DeserializationStrategy<BaseSection.BaseQuestion> {
        val typeStr = element.jsonObject["type"]?.jsonPrimitive?.contentOrNull
            ?: throw SerializationException("Missing 'type' field")

        val type = getEnumFromSerialName(typeStr, QuestionType::class.java)
        return type?.serializer ?: throw SerializationException("Unsupported Question type: $typeStr")
    }
}

@OptIn(ExperimentalSerializationApi::class)
object BigDecimalSerializer : KSerializer<BigDecimal> {

    override val descriptor = PrimitiveSerialDescriptor("java.math.BigDecimal", PrimitiveKind.DOUBLE)

    /**
     * If decoding JSON uses [JsonDecoder.decodeJsonElement] to get the raw content,
     * otherwise decodes using [Decoder.decodeString].
     */
    override fun deserialize(decoder: Decoder): BigDecimal =
        when (decoder) {
            is JsonDecoder -> decoder.decodeJsonElement().jsonPrimitive.content.toBigDecimal()
            else -> decoder.decodeString().toBigDecimal()
        }

    /**
     * If encoding JSON uses [JsonUnquotedLiteral] to encode the exact [BigDecimal] value.
     *
     * Otherwise, [value] is encoded using encodes using [Encoder.encodeString].
     */
    override fun serialize(encoder: Encoder, value: BigDecimal) =
        when (encoder) {
            is JsonEncoder -> encoder.encodeJsonElement(JsonUnquotedLiteral(value.toPlainString()))
            else -> encoder.encodeString(value.toPlainString())
        }
}