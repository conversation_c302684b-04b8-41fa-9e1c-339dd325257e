import { ChartType } from "@oneteam/onetheme";

import { Question, QuestionTypes } from "@src/types/Question";
import {
  ChartConfig,
  SelectQuestionProperties
} from "@src/types/QuestionProperties";

export const generateMockData = (
  columns: Question[],
  rowCount: number
): Record<string, string>[] => {
  const mockRows: Record<string, string>[] = [];

  for (let i = 1; i <= rowCount; i++) {
    const row: Record<string, string> = {};
    columns.forEach(col => {
      switch (col.type) {
        case QuestionTypes.TEXT:
          row[col.identifier] = `${col.identifier}_${i}`;
          break;
        case QuestionTypes.NUMBER:
          // Generate a random number between 1 and 10000 as a string
          row[col.identifier] = (
            Math.floor(Math.random() * 10000) + 1
          ).toString();
          break;
        case QuestionTypes.DATE:
          {
            const date = new Date();
            date.setDate(date.getDate() + i);
            row[col.identifier] = date.toISOString().split("T")[0];
          }
          break;
        case QuestionTypes.SELECT:
          {
            // Use the first option value if available, else empty string
            const options = (col.properties as SelectQuestionProperties)
              ?.options;
            if (options && options.length > 0) {
              const randomIndex = Math.floor(Math.random() * options.length);
              row[col.identifier] = options[randomIndex].value as string;
            } else {
              row[col.identifier] = "";
            }
          }
          break;
        case QuestionTypes.BOOLEAN:
          row[col.identifier] = i % 2 === 0 ? "true" : "false";
          break;
        default:
          row[col.identifier] = "";
      }
    });
    mockRows.push(row);
  }
  return mockRows;
};

/**
 * Maps column identifiers to their display names (array version)
 */
export function mapIdsToNames(
  ids: string[] | undefined,
  columns: Question[] = []
): string[] {
  if (!ids) return [];
  return ids.map(id => {
    const col = columns.find(c => c.id === id);
    return col?.identifier || id;
  });
}

/**
 * Maps a single column identifier to its display name
 */
export function mapIdToName(
  id: string | undefined,
  columns: Question[] = []
): string {
  if (!id) return "";
  const col = columns.find(c => c.id === id);
  return col?.identifier || id;
}

/**
 * Generic function to transform chart config properties
 */
export function transformChartConfigProperty<T extends ChartConfig>(
  config: T,
  propertyKey: keyof T,
  transformer: (value: any) => any
): T {
  return {
    ...config,
    [propertyKey]: transformer(config[propertyKey])
  };
}

/**
 * Transforms multiple chart config properties at once
 */
export function transformChartConfigProperties<T extends ChartConfig>(
  config: T,
  transformations: Partial<Record<keyof T, (value: any) => any>>
): T {
  let result = { ...config };

  for (const [key, transformer] of Object.entries(transformations)) {
    if (transformer && key in result) {
      result = transformChartConfigProperty(
        result,
        key as keyof T,
        transformer
      );
    }
  }

  return result;
}

/**
 * Maps chart config identifiers to display names and converts to Chart component format
 */
export function mapChartConfigToNames(
  chartConfig: any, // Question properties format
  columns: Question[] = []
): ChartConfig {
  // Chart component format
  if (
    chartConfig.type === ChartType.LINE ||
    chartConfig.type === ChartType.BAR
  ) {
    return {
      type: chartConfig.type,
      subType: chartConfig.subType,
      swapColumns: chartConfig.swapColumns,
      xAxis: mapIdsToNames(chartConfig.xAxis, columns),
      series: mapIdsToNames(chartConfig.series, columns)
    };
  }

  if (chartConfig.type === ChartType.PIE) {
    // Convert from question properties format (series: string[]) to chart component format (series: string)
    const seriesArray = mapIdsToNames(chartConfig.series, columns);
    return {
      type: chartConfig.type,
      subType: chartConfig.subType,
      swapColumns: chartConfig.swapColumns,
      showToolbox: chartConfig.showToolbox,
      groupBy: mapIdsToNames(chartConfig.groupBy, columns),
      series: seriesArray[0] || "", // Convert array to string
      rowIndex: chartConfig.rowIndex
    };
  }

  return chartConfig;
}

/**
 * Applies pie chart filter transformations
 */
export function applyPieChartFilter(
  chartConfig: ChartConfig,
  pieFilter: string | number | undefined,
  formattedData: Record<string, string>[] = []
): ChartConfig {
  if (!pieFilter || chartConfig.type !== ChartType.PIE) {
    return chartConfig;
  }

  const result = { ...chartConfig } as any;

  if (result.swapColumns) {
    const rowIndex = Number(pieFilter);
    result.rowIndex = rowIndex;

    // Get the series value from the selected row
    const selectedRow = formattedData[rowIndex];
    if (
      selectedRow &&
      Array.isArray(result.series) &&
      result.series.length > 0
    ) {
      result.series = selectedRow[result.series[0]] || result.series[0];
    }
  } else {
    result.rowIndex = undefined;
    result.series = pieFilter as string;
  }

  return result;
}

/**
 * Complete chart config formatting pipeline
 */
export function formatChartConfig(
  rawConfig: any, // Question properties format
  columns: Question[] = [],
  pieFilter?: string | number,
  formattedData?: Record<string, string>[]
): ChartConfig | undefined {
  // Chart component format
  if (!rawConfig) return undefined;

  // Step 1: Map IDs to names and convert to chart component format
  let config = mapChartConfigToNames(rawConfig, columns);

  // Step 2: Apply pie chart filter if applicable
  config = applyPieChartFilter(config, pieFilter, formattedData);

  return config;
}
