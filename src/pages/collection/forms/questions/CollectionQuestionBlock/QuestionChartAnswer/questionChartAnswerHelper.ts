import { Question, QuestionTypes } from "@src/types/Question";
import { SelectQuestionProperties } from "@src/types/QuestionProperties";

export const generateMockData = (
  columns: Question[],
  rowCount: number
): Record<string, string>[] => {
  const mockRows: Record<string, string>[] = [];

  for (let i = 1; i <= rowCount; i++) {
    const row: Record<string, string> = {};
    columns.forEach(col => {
      switch (col.type) {
        case QuestionTypes.TEXT:
          row[col.identifier] = `${col.identifier}_${i}`;
          break;
        case QuestionTypes.NUMBER:
          // Generate a random number between 1 and 10000 as a string
          row[col.identifier] = (
            Math.floor(Math.random() * 10000) + 1
          ).toString();
          break;
        case QuestionTypes.DATE:
          {
            const date = new Date();
            date.setDate(date.getDate() + i);
            row[col.identifier] = date.toISOString().split("T")[0];
          }
          break;
        case QuestionTypes.SELECT:
          {
            // Use the first option value if available, else empty string
            const options = (col.properties as SelectQuestionProperties)
              ?.options;
            if (options && options.length > 0) {
              const randomIndex = Math.floor(Math.random() * options.length);
              row[col.identifier] = options[randomIndex].value as string;
            } else {
              row[col.identifier] = "";
            }
          }
          break;
        case QuestionTypes.BOOLEAN:
          row[col.identifier] = i % 2 === 0 ? "true" : "false";
          break;
        default:
          row[col.identifier] = "";
      }
    });
    mockRows.push(row);
  }
  return mockRows;
};
