import React, { useEffect, useMemo, useState } from "react";

import {
  Box,
  Chart,
  ChartConfig,
  ChartType,
  Inline,
  PillSelect,
  SelectValue,
  Stack,
  Text
} from "@oneteam/onetheme";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";

import { Question, QuestionTypes } from "@src/types/Question";
import {
  ChartQuestionProperties,
  TableQuestionProperties
} from "@src/types/QuestionProperties";

import "./QuestionChartAnswer.scss";

const mockDataRowNumber = 3;

export const QuestionChartAnswer = ({
  chartAnswerAccessor = "",
  question,
  isPreview = false,
  parentQuestion
}: {
  chartAnswerAccessor?: string;
  question: Question<ChartQuestionProperties>;
  isPreview?: boolean;
  parentQuestion?: Question<TableQuestionProperties>;
}) => {
  const [pieFilter, setPieFilter] = useState<SelectValue | undefined>("");
  // const { docChange, formData } = useCollectionFormContext();

  function generateMockData(
    columns: Question[],
    rowCount: number
  ): Record<string, string>[] {
    const mockRows: Record<string, string>[] = [];

    for (let i = 1; i <= rowCount; i++) {
      const row: Record<string, string> = {};
      columns.forEach(col => {
        switch (col.type) {
          case QuestionTypes.TEXT:
            row[col.identifier] = `${col.identifier}_${i}`;
            break;
          case QuestionTypes.NUMBER:
            // Generate a random number between 1000 and 10000 as a string
            row[col.identifier] = (
              Math.floor(Math.random() * 9000) + 1000
            ).toString();
            break;
          case QuestionTypes.DATE:
            // Generate a date string, e.g., today + i days
            const date = new Date();
            date.setDate(date.getDate() + i);
            row[col.identifier] = date.toISOString().split("T")[0];
            break;
          case QuestionTypes.SELECT:
            {
              // Use the first option value if available, else empty string
              const options = col.properties?.options;
              row[col.identifier] =
                options && options.length > 0 ? options[0].value : "";
            }
            break;
          case QuestionTypes.BOOLEAN:
            row[col.identifier] = i % 2 === 0 ? "true" : "false";
            break;
          default:
            row[col.identifier] = "";
        }
      });
      mockRows.push(row);
    }
    return mockRows;
  }

  const mockData = useMemo(() => {
    return generateMockData(
      parentQuestion?.properties?.columns || [],
      mockDataRowNumber
    );
  }, [parentQuestion?.properties?.columns]);

  const config = useMemo(() => {
    const baseConfig = { ...question?.properties?.chartConfig };
    if (baseConfig.type === ChartType.PIE) {
      if (baseConfig.swapColumns) {
        baseConfig.rowIndex = pieFilter as number;
        // Optionally reset series if needed
      } else {
        baseConfig.series = [pieFilter as string];
      }
    }
    return baseConfig;
  }, [question?.properties?.chartConfig, pieFilter]);

  const pieChartFilterOptions = useMemo(() => {
    if (question?.properties?.chartConfig.type !== ChartType.PIE) return [];

    if (question?.properties?.chartConfig.swapColumns) {
      // Use seriesBy column values from mockData
      const seriesByKey = question?.properties?.chartConfig.series?.[0];
      if (!seriesByKey) return [];
      return mockData.map((row, index) => ({
        label: row[seriesByKey] ?? `Row ${index + 1}`,
        value: index
      }));
    } else {
      // Use series keys as options
      return (question?.properties?.chartConfig.series || []).map(
        (seriesKey: string) => ({
          label: seriesKey,
          value: seriesKey
        })
      );
    }
  }, [question?.properties?.chartConfig, mockData]);

  const handlePieChartFilterChange = (value: SelectValue | undefined) => {
    setPieFilter(value);
  };

  return (
    <Stack gap="100">
      <Inline width="100" spaceBetween>
        <Text size="m" color="text-tertiary">
          {question.text}
        </Text>
        {config.type === ChartType.PIE && (
          <PillSelect
            label="Filter"
            value={pieFilter}
            options={pieChartFilterOptions}
            handleChange={value => handlePieChartFilterChange(value)}
          />
        )}
      </Inline>
      <Box className="chart-preview-container">
        <Chart
          data={mockData}
          config={config as ChartConfig}
          width="100"
          height="100"
        />
      </Box>
    </Stack>
  );
};
