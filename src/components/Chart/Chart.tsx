import React from "react";

import ReactECharts from "echarts-for-react";
import { cloneDeep, merge } from "lodash";

import { Box } from "../../fermions";
import {
  getBaseOptions,
  getOptionsForSubType,
  getOptionsForType
} from "./ChartOptions";
import { ChartProps, defaultSubType } from "./ChartTypes";
import { useChartData } from "./useChart";

export const Chart = ({
  title,
  data,
  config,
  advancedOptions,
  ...rest
}: ChartProps) => {
  const baseOptions = getBaseOptions({
    title,
    type: config.type,
    showToolbox: config.showToolbox
  });
  const typeOptions = getOptionsForType[config.type]();
  const subTypeOptions = getOptionsForSubType[config.type]({
    subType: config.subType ?? defaultSubType[config.type]
  });
  const chartOptions = merge(
    {},
    baseOptions,
    typeOptions,
    subTypeOptions,
    advancedOptions
  );
  const chartData = useChartData(config, chartOptions, data);
  console.log("chartData", chartData);
  const optionWithData = merge(
    {},
    cloneDeep(chartOptions),
    cloneDeep(chartData)
  );

  return (
    <Box className="chart" {...rest}>
      <ReactECharts
        option={optionWithData}
        style={{ width: "100%", height: "100%" }}
        notMerge={true} // force replace instead of patch
        lazyUpdate={true} // optional, avoids extra renders
      />
    </Box>
  );
};
