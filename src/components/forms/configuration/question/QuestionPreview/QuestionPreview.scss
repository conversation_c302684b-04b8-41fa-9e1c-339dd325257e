.question-preview {
  margin-top: var(--spacing-075);
  background-color: var(--color-surface-primary);
  padding: var(--spacing-200);
  border-radius: var(--border-radius-rounded);

  .accordion__content--open {
    max-height: 35vh;
  }
}

.question-table-preview__tabs {
  align-items: flex-end !important;
}

.question-table-preview__header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--color-surface-primary);
}
