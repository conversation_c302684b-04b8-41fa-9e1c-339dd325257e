import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useOutletContext } from "react-router-dom";

import { Doc } from "@automerge/automerge-repo";
import {
  Breadcrumbs,
  BreadcrumbsItem,
  ChartType,
  Stack
} from "@oneteam/onetheme";

import {
  createChartQuestion,
  createQuestion,
  getByPath,
  getChildQuestionTypeOptions,
  getQuestionTypeOptionsForForms
} from "@helpers/configurationFormHelper.ts";

import { QuestionType } from "@components/forms/QuestionType/QuestionType.tsx";
import { FloatingModal } from "@components/shared/FloatingModal/FloatingModal.tsx";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { ConfigurationFormMode } from "@src/types/FormConfiguration.ts";
import {
  ChartQuestionTypes,
  Question,
  QuestionTypes
} from "@src/types/Question.ts";
import { ChartQuestionProperties } from "@src/types/QuestionProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { QuestionPreview } from "../QuestionPreview/QuestionPreview.tsx";
import { ConfigurationQuestionFields } from "./QuestionConfigurationFields/QuestionConfigurationFields.tsx";
import { QuestionTypeSelect } from "./QuestionTypeSelect/QuestionTypeSelect.tsx";

export const ConfigurationQuestionModal = ({
  mode,
  questionPath,
  onClose,
  onUp,
  isMultiLevelQuestion = false
}: {
  mode: `${ConfigurationFormMode}`;
  questionPath: string;
  onClose: () => void;
  onUp?: () => void;
  isMultiLevelQuestion?: boolean;
}) => {
  const [width, setWidth] = useState<string | undefined>("30vw");
  const { document, docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  const question = useMemo(() => {
    return getByPath<Question>(document, questionPath?.split("."));
  }, [document, questionPath]);

  const parentQuestion = useMemo(() => {
    if (!questionPath || !isMultiLevelQuestion) {
      return null;
    }
    const parentPath = questionPath.split(".").slice(0, -3);
    return getByPath<Question>(document, parentPath);
  }, [document, isMultiLevelQuestion, questionPath]);

  const questionTypeOptions = useMemo(() => {
    if (parentQuestion?.type) {
      if (question?.type === QuestionTypes.CHART) {
        return ChartQuestionTypes;
      }
      return getChildQuestionTypeOptions(parentQuestion.type, {
        forUserSelection: true
      });
    }
    return getQuestionTypeOptionsForForms();
  }, [parentQuestion?.type, question?.type]);

  const currentQuestionType = useMemo(() => {
    if (question?.type === QuestionTypes.CHART) {
      return (question?.properties as ChartQuestionProperties)?.chartConfig
        ?.type;
    }
    return question?.type;
  }, [question]);

  const isChartType = (value: QuestionTypes | ChartType): value is ChartType =>
    Object.values(ChartType).includes(value as ChartType);

  const handleQuestionTypeChange = useCallback(
    (newType: QuestionTypes | ChartType) => {
      docChange(d => {
        const q = getByPath<Question>(d, questionPath.split("."));
        if (!q) {
          console.error("Question not found", questionPath);
          return;
        }
        if (isChartType(newType)) {
          const newChart = createChartQuestion(newType);
          q.properties = newChart.properties;
        } else {
          q.type = newType;
          const newQuestion = createQuestion(newType);
          q.properties = newQuestion.properties;
        }
      });
    },
    [docChange, questionPath]
  );

  useEffect(() => {
    // path reference is invalid, close modal
    if (questionPath && questionPath !== "none" && !question) {
      console.error("invalid path", questionPath);
      onClose();
    }
  }, [onClose, question, questionPath]);

  const questionSelect = useMemo(
    () => (
      <QuestionTypeSelect
        type={currentQuestionType}
        path={questionPath.split(".")}
        options={questionTypeOptions}
        handleChange={handleQuestionTypeChange}
        disabled={mode !== ConfigurationFormMode.EDIT}
      />
    ),
    [
      mode,
      questionPath,
      currentQuestionType,
      questionTypeOptions,
      handleQuestionTypeChange
    ]
  );
  if (!questionPath || questionPath === "none") {
    return <></>;
  }

  if (!question) {
    return <></>;
  }

  return (
    <FloatingModal
      key={questionPath}
      className="question-configuration-modal"
      headingOverride={
        <Stack
          gap="075"
          style={{ height: "var(--spacing-300)" }}
          alignment="left"
        >
          {isMultiLevelQuestion ? (
            <Breadcrumbs>
              {parentQuestion && (
                <BreadcrumbsItem
                  text={parentQuestion!.text ?? "< Back"}
                  href={""}
                  leftElement={
                    <QuestionType type={parentQuestion!.type} iconOnly />
                  }
                  onClick={onUp}
                />
              )}
              {questionSelect}
            </Breadcrumbs>
          ) : (
            questionSelect
          )}
        </Stack>
      }
      onClose={onClose}
      width={width}
      setWidth={setWidth}
    >
      <ConfigurationQuestionFields
        question={question}
        path={questionPath.split(".")}
        disabled={mode !== ConfigurationFormMode.EDIT}
        parentQuestion={parentQuestion ?? undefined}
      />
      <QuestionPreview
        question={question}
        parentQuestion={parentQuestion ?? undefined}
      />
    </FloatingModal>
  );
};
